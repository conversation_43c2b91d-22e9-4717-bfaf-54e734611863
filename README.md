# Juli Get Info

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Deployment

### Standard Deployment

To deploy this application in a production environment:

1. Make sure you have Node.js 18+ installed
2. Install dependencies:
   ```bash
   npm install
   ```
3. Build the application:
   ```bash
   npm run build
   ```
4. Start the application:
   ```bash
   npm start
   ```

The application will be available on port 3000 by default.

### Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name
```

### Docker Deployment

This application can also be deployed using Docker. See the [Dockerfile](Dockerfile) and [docker-compose.yml](docker-compose.yml) for more details.

1. Build the Docker image:
   ```bash
   docker build -t juli-get-info .
   ```
2. Run the container:
   ```bash
   docker run -p 3000:3000 juli-get-info
   ```

### Docker Compose Deployment

For a complete setup with database:

1. Update the database credentials in [docker-compose.yml](docker-compose.yml)
2. Run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

This will start both the application and MySQL database.