-- 数据库迁移脚本：添加产品字段和分类
-- 执行时间：2025-08-03

USE juli_web;

-- 1. 修改 demo_products 表，添加缺失的字段
ALTER TABLE demo_products 
ADD COLUMN features TEXT COMMENT '产品特点' AFTER description,
ADD COLUMN applications TEXT COMMENT '应用领域' AFTER features,
ADD COLUMN specifications TEXT COMMENT '技术参数' AFTER applications,
ADD COLUMN category_id INT COMMENT '分类ID' AFTER specifications,
ADD COLUMN status ENUM('active', 'inactive', 'discontinued') DEFAULT 'active' COMMENT '产品状态' AFTER category_id,
ADD COLUMN seo_title VARCHAR(255) COMMENT 'SEO标题' AFTER status,
ADD COLUMN seo_description TEXT COMMENT 'SEO描述' AFTER seo_title;

-- 2. 添加外键约束
ALTER TABLE demo_products 
ADD CONSTRAINT fk_demo_products_category 
FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL;

-- 3. 添加索引
ALTER TABLE demo_products 
ADD INDEX idx_demo_category_id (category_id),
ADD INDEX idx_demo_status (status);

-- 4. 插入新的产品分类
INSERT INTO categories (name, slug, type, description, sort_order) VALUES
('小型等离子清洗设备', 'small-plasma-equipment', 'product', '小型等离子清洗设备系列', 4);

-- 5. 更新现有产品的分类关联
-- 获取分类ID并更新产品
SET @small_vacuum_id = (SELECT id FROM categories WHERE slug = 'xxzkdlz' AND type = 'product');
SET @large_vacuum_id = (SELECT id FROM categories WHERE slug = 'dxzkdlz' AND type = 'product');
SET @atmospheric_id = (SELECT id FROM categories WHERE slug = 'dqdlz' AND type = 'product');
SET @small_equipment_id = (SELECT id FROM categories WHERE slug = 'small-plasma-equipment' AND type = 'product');

-- 更新现有产品的分类关联
UPDATE demo_products SET 
  category_id = @atmospheric_id,
  status = 'active'
WHERE category = 'atmospheric';

UPDATE demo_products SET 
  category_id = @small_vacuum_id,
  status = 'active'
WHERE category = 'vacuum' AND name LIKE '%小型%';

UPDATE demo_products SET 
  category_id = @large_vacuum_id,
  status = 'active'
WHERE category = 'vacuum' AND name LIKE '%大型%';

-- 6. 更新现有产品的详细信息
UPDATE demo_products SET 
  features = '• 低温不伤产品，产生中性等离子，不带静电
• 增加表面活性，去除灰尘清除表面油污
• 改变表面分子结构，精细清洗和去静电
• 提高附着力亲水性，提升表面粘接的可靠性和持久性
• 喷头可选，支持定制，气压可调节',
  applications = '金属/光电子/汽车制造/纺织品/生物医疗/航天航空/电子/半导体/LED/塑胶等',
  specifications = '• 设备功率：1000W（可调）
• 主机尺寸：420×202×427mm
• 运行气压：0.25MPa
• 输入电压：AC220V/50Hz
• 处理速度：0.1M-0.5M/S
• 机器重量：18KG
• 处理宽度：≤100MM（可定制）
• 输入气压：0.15-0.4MPa
• 喷枪线缆长度：3000mm（可定制）
• 处理高度：5-15mm

喷头尺寸：
常规款：20mm、32mm、45mm、50mm、56mm
加大款：70mm、80mm、90mm、100mm（支持定制）',
  seo_title = '旋转式等离子表面处理机 JC-DLZ-1000X - 专业等离子清洗设备',
  seo_description = '旋转式等离子表面处理机JC-DLZ-1000X，适用于电子、汽车、航空等行业的表面处理，功率可调，支持定制。'
WHERE name = '旋转式等离子表面处理机';

-- 7. 创建产品分类视图，方便查询
CREATE OR REPLACE VIEW v_products_with_categories AS
SELECT 
  p.*,
  c.name as category_name,
  c.slug as category_slug,
  c.description as category_description
FROM demo_products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.is_active = 1 AND c.type = 'product'
ORDER BY p.sort_order ASC, p.created_at DESC;

-- 8. 创建分类统计视图
CREATE OR REPLACE VIEW v_category_stats AS
SELECT 
  c.id,
  c.name,
  c.slug,
  c.description,
  c.sort_order,
  COUNT(p.id) as product_count
FROM categories c
LEFT JOIN demo_products p ON c.id = p.category_id AND p.is_active = 1
WHERE c.type = 'product'
GROUP BY c.id, c.name, c.slug, c.description, c.sort_order
ORDER BY c.sort_order ASC;
