/* 等离子清洗技术官网 - 设计系统 CSS 变量 */

:root {
  /* ========== 颜色系统 ========== */
  
  /* 主色调 */
  --primary-blue: #2563eb;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1d4ed8;
  --primary-blue-50: rgba(37, 99, 235, 0.05);
  --primary-blue-100: rgba(37, 99, 235, 0.1);
  --primary-blue-200: rgba(37, 99, 235, 0.2);
  
  /* 中性色 */
  --neutral-white: #ffffff;
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;
  
  /* 功能色 */
  --success: #10b981;
  --success-light: #34d399;
  --warning: #f59e0b;
  --warning-light: #fbbf24;
  --error: #ef4444;
  --error-light: #f87171;
  --info: #06b6d4;
  --info-light: #22d3ee;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  --gradient-secondary: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  --gradient-hero: linear-gradient(135deg, #1e3a8a 0%, #2563eb 50%, #3b82f6 100%);
  
  /* ========== 字体系统 ========== */
  
  /* 字体家族 */
  --font-primary: 'Alibaba PuHuiTi 2.0', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
  
  /* 字体大小 */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */
  
  /* 字体粗细 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.4;
  --leading-relaxed: 1.6;
  --leading-loose: 1.8;
  
  /* ========== 间距系统 ========== */
  
  --space-1: 0.25rem;      /* 4px */
  --space-2: 0.5rem;       /* 8px */
  --space-3: 0.75rem;      /* 12px */
  --space-4: 1rem;         /* 16px */
  --space-5: 1.25rem;      /* 20px */
  --space-6: 1.5rem;       /* 24px */
  --space-8: 2rem;         /* 32px */
  --space-10: 2.5rem;      /* 40px */
  --space-12: 3rem;        /* 48px */
  --space-16: 4rem;        /* 64px */
  --space-20: 5rem;        /* 80px */
  --space-24: 6rem;        /* 96px */
  --space-32: 8rem;        /* 128px */
  
  /* ========== 圆角系统 ========== */
  
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;
  
  /* ========== 阴影系统 ========== */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* ========== 布局系统 ========== */
  
  /* 容器宽度 */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1440px;
  --container-content: 1200px;
  
  /* 断点 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* ========== 动画系统 ========== */
  
  /* 过渡时间 */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
  
  /* 缓动函数 */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* ========== Z-index 层级 ========== */
  
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-modal: 1000;
  --z-popover: 1010;
  --z-tooltip: 1020;
  --z-notification: 1030;
  
  /* ========== 组件特定变量 ========== */
  
  /* 导航栏 */
  --navbar-height: 80px;
  --navbar-bg: var(--neutral-white);
  --navbar-shadow: var(--shadow-sm);
  
  /* 按钮 */
  --button-height-sm: 32px;
  --button-height-md: 40px;
  --button-height-lg: 48px;
  --button-padding-x: var(--space-6);
  --button-padding-y: var(--space-3);
  
  /* 卡片 */
  --card-bg: var(--neutral-white);
  --card-border: var(--neutral-200);
  --card-radius: var(--radius-lg);
  --card-shadow: var(--shadow-base);
  --card-padding: var(--space-6);
  
  /* 输入框 */
  --input-height: 40px;
  --input-bg: var(--neutral-white);
  --input-border: var(--neutral-300);
  --input-border-focus: var(--primary-blue);
  --input-radius: var(--radius-base);
  --input-padding-x: var(--space-3);
}

/* ========== 深色模式变量 ========== */
@media (prefers-color-scheme: dark) {
  :root {
    --neutral-white: #1f2937;
    --neutral-50: #374151;
    --neutral-100: #4b5563;
    --neutral-200: #6b7280;
    --neutral-300: #9ca3af;
    --neutral-400: #d1d5db;
    --neutral-500: #e5e7eb;
    --neutral-600: #f3f4f6;
    --neutral-700: #f9fafb;
    --neutral-800: #ffffff;
    --neutral-900: #ffffff;
    
    --card-bg: var(--neutral-800);
    --navbar-bg: var(--neutral-800);
  }
}

/* ========== 基础样式重置 ========== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--neutral-900);
  background-color: var(--neutral-white);
}

body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========== 实用工具类 ========== */
.container {
  max-width: var(--container-content);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-primary { color: var(--primary-blue); }
.text-secondary { color: var(--neutral-600); }
.text-muted { color: var(--neutral-500); }

.bg-primary { background-color: var(--primary-blue); }
.bg-white { background-color: var(--neutral-white); }
.bg-gray-50 { background-color: var(--neutral-50); }

.rounded { border-radius: var(--radius-base); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow-base); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* ========== 组件样式 ========== */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--button-padding-y) var(--button-padding-x);
  border-radius: var(--radius-base);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-in-out);
}

.btn-primary {
  background-color: var(--primary-blue);
  color: var(--neutral-white);
  border-color: var(--primary-blue);
}

.btn-primary:hover {
  background-color: var(--primary-blue-dark);
  border-color: var(--primary-blue-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-blue);
  border-color: var(--primary-blue);
}

.btn-secondary:hover {
  background-color: var(--primary-blue-50);
}

.btn-warning {
  background-color: var(--warning);
  color: var(--neutral-white);
  border-color: var(--warning);
}

.btn-warning:hover {
  background-color: var(--warning-light);
  border-color: var(--warning-light);
}

.btn-danger {
  background-color: var(--error);
  color: var(--neutral-white);
  border-color: var(--error);
}

.btn-danger:hover {
  background-color: var(--error-light);
  border-color: var(--error-light);
}

.btn-lg {
  height: var(--button-height-lg);
  padding: var(--space-3) var(--space-8);
  font-size: var(--text-base);
}

.btn-sm {
  height: var(--button-height-sm);
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
}

/* 卡片组件 */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  padding: var(--card-padding);
  transition: box-shadow var(--duration-200) var(--ease-in-out);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.card-header {
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--neutral-200);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--neutral-900);
  margin-bottom: var(--space-2);
}

.card-description {
  color: var(--neutral-600);
  font-size: var(--text-sm);
}

/* 导航栏组件 */
.navbar {
  height: var(--navbar-height);
  background-color: var(--navbar-bg);
  box-shadow: var(--navbar-shadow);
  position: sticky;
  top: 0;
  z-index: var(--z-50);
  backdrop-filter: blur(8px);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: var(--container-content);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.navbar-brand {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--neutral-900);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  list-style: none;
}

.navbar-link {
  color: var(--neutral-700);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: color var(--duration-200) var(--ease-in-out);
}

.navbar-link:hover,
.navbar-link.active {
  color: var(--primary-blue);
}

/* 表单组件 */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: var(--font-medium);
  color: var(--neutral-700);
}

.form-input {
  width: 100%;
  height: var(--input-height);
  padding: 0 var(--input-padding-x);
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: var(--input-radius);
  font-size: var(--text-sm);
  transition: border-color var(--duration-200) var(--ease-in-out);
}

.form-input:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px var(--primary-blue-100);
}

.form-input::placeholder {
  color: var(--neutral-400);
}

/* 网格系统 */
.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }
