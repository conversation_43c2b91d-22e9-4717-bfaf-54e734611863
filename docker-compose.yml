version: '3.8'

services:
  app:
    build: .
    ports:
      - "4001:3000"
    environment:
      - DB_HOST=db
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=password
      - DB_NAME=juli_web
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=juli_web
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/1-schema.sql
      - ./database/sample-data.sql:/docker-entrypoint-initdb.d/2-sample-data.sql
    restart: unless-stopped

volumes:
  mysql_data: