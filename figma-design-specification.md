# 等离子清洗技术官网 - Figma设计规范文档

## 📋 项目概述

本项目为等离子清洗技术服务公司设计的完整官网系统，包含前台展示和后台管理两大模块。设计风格专业、现代，体现科技感和工业感。

## 🎨 设计系统

### 颜色规范

#### 主色调
- **主蓝色**: #2563eb (RGB: 37, 99, 235)
- **主蓝色-浅**: #3b82f6 (RGB: 59, 130, 246)  
- **主蓝色-深**: #1d4ed8 (RGB: 29, 78, 216)

#### 功能色
- **成功绿**: #10b981 (RGB: 16, 185, 129)
- **警告橙**: #f59e0b (RGB: 245, 158, 11)
- **错误红**: #ef4444 (RGB: 239, 68, 68)
- **信息蓝**: #06b6d4 (RGB: 6, 182, 212)

#### 中性色
- **白色**: #ffffff
- **灰色-50**: #f9fafb
- **灰色-100**: #f3f4f6
- **灰色-200**: #e5e7eb
- **灰色-300**: #d1d5db
- **灰色-400**: #9ca3af
- **灰色-500**: #6b7280
- **灰色-600**: #4b5563
- **灰色-700**: #374151
- **灰色-800**: #1f2937
- **灰色-900**: #111827

### 字体规范

#### 字体家族
- **主字体**: Alibaba PuHuiTi 2.0 (阿里巴巴普惠体)
- **备用字体**: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif

#### 字体大小层级
- **超大标题**: 48px (3rem) - 首页主标题
- **大标题**: 36px (2.25rem) - 页面标题
- **中标题**: 28px (1.75rem) - 区块标题
- **小标题**: 24px (1.5rem) - 卡片标题
- **子标题**: 20px (1.25rem) - 组件标题
- **正文**: 16px (1rem) - 主要内容
- **小字**: 14px (0.875rem) - 辅助信息
- **极小字**: 12px (0.75rem) - 标签、按钮

#### 字体粗细
- **极细**: 300 (Light)
- **正常**: 400 (Regular)
- **中等**: 500 (Medium)
- **半粗**: 600 (Semi Bold)
- **粗体**: 700 (Bold)

### 间距系统

#### 标准间距单位
- **4px**: 最小间距单位
- **8px**: 小间距
- **12px**: 中小间距
- **16px**: 中等间距
- **20px**: 中大间距
- **24px**: 大间距
- **32px**: 很大间距
- **40px**: 超大间距
- **48px**: 区块间距
- **64px**: 大区块间距
- **80px**: 超大区块间距

### 圆角系统

- **无圆角**: 0px
- **小圆角**: 4px - 按钮、输入框
- **中圆角**: 8px - 卡片、图片
- **大圆角**: 12px - 大卡片、容器
- **超大圆角**: 16px - 特殊容器
- **完全圆角**: 9999px - 圆形按钮

### 阴影系统

- **轻微阴影**: 0 1px 3px rgba(0,0,0,0.1)
- **基础阴影**: 0 4px 6px rgba(0,0,0,0.1)
- **中等阴影**: 0 10px 15px rgba(0,0,0,0.1)
- **重阴影**: 0 20px 25px rgba(0,0,0,0.1)
- **超重阴影**: 0 25px 50px rgba(0,0,0,0.25)

## 📐 布局规范

### 页面布局
- **页面宽度**: 1440px (桌面端)
- **内容区域**: 1200px (居中布局)
- **左右边距**: 120px
- **导航栏高度**: 80px

### 网格系统
- **列数**: 12列
- **列间距**: 24px
- **断点**:
  - 移动端: < 768px
  - 平板端: 768px - 1024px
  - 桌面端: > 1024px

### 组件尺寸

#### 按钮
- **小按钮**: 32px 高度
- **中按钮**: 40px 高度
- **大按钮**: 48px 高度
- **内边距**: 12px 24px

#### 输入框
- **标准高度**: 40px
- **内边距**: 0 12px
- **边框**: 1px solid #d1d5db

#### 卡片
- **内边距**: 24px
- **圆角**: 12px
- **边框**: 1px solid #e5e7eb

## 🧩 组件库

### 按钮组件
1. **主要按钮** - 蓝色背景，白色文字
2. **次要按钮** - 透明背景，蓝色边框
3. **警告按钮** - 橙色背景，白色文字
4. **危险按钮** - 红色背景，白色文字

### 卡片组件
1. **基础卡片** - 白色背景，轻微阴影
2. **产品卡片** - 包含图片、标题、描述、按钮
3. **统计卡片** - 数字展示，带图标
4. **特色卡片** - 突出显示的内容卡片

### 导航组件
1. **主导航** - 水平导航栏
2. **面包屑** - 页面路径导航
3. **侧边导航** - 后台管理侧边栏

### 表单组件
1. **输入框** - 文本输入
2. **选择框** - 下拉选择
3. **按钮组** - 多个按钮组合
4. **标签** - 分类标签

## 📱 页面设计

### 前台页面

#### 1. 首页 (已完成)
- **Hero区域**: 主标题、副标题、CTA按钮、背景图
- **核心优势**: 3个特色卡片展示
- **产品展示**: 4个产品卡片网格
- **技术优势**: 数据统计展示
- **Footer**: 联系信息、快速链接

#### 2. 产品详情页 (已完成)
- **面包屑导航**: 页面路径
- **产品信息**: 主图、标题、型号、特点
- **技术参数**: 详细规格表格
- **应用场景**: 使用场景列表
- **相关产品**: 推荐产品卡片

#### 3. 产品分类页 (已完成)
- **页面标题**: 产品中心
- **分类筛选**: 产品类别选择
- **产品网格**: 3列产品卡片布局
- **分页导航**: 页面切换

#### 4. 关于我们页 (已完成)
- **Hero区域**: 公司介绍
- **公司历程**: 发展时间线
- **团队介绍**: 核心团队成员
- **企业文化**: 价值观展示

### 后台页面

#### 1. 产品管理页 (已完成)
- **侧边导航**: 功能菜单
- **统计卡片**: 数据概览
- **数据表格**: 产品列表
- **操作按钮**: 编辑、删除功能

#### 2. 分类管理页 (待完成)
- **分类树**: 层级结构展示
- **添加分类**: 新增功能
- **编辑分类**: 修改功能

#### 3. 数据统计页 (待完成)
- **图表展示**: 数据可视化
- **关键指标**: KPI展示
- **趋势分析**: 数据趋势

## 🎯 交互规范

### 悬停效果
- **按钮**: 颜色加深，轻微缩放
- **卡片**: 阴影增强，轻微上浮
- **链接**: 颜色变化，下划线显示

### 点击反馈
- **按钮**: 轻微缩放动画
- **卡片**: 点击波纹效果
- **链接**: 颜色瞬间变化

### 加载状态
- **按钮**: 加载图标，禁用状态
- **页面**: 骨架屏或加载动画
- **数据**: 加载指示器

### 错误状态
- **表单**: 红色边框，错误提示
- **页面**: 404页面设计
- **网络**: 网络错误提示

## 📊 响应式设计

### 移动端适配
- **导航**: 汉堡菜单
- **网格**: 单列布局
- **字体**: 适当缩小
- **间距**: 减少边距

### 平板端适配
- **网格**: 2列布局
- **导航**: 简化菜单
- **图片**: 适应屏幕

### 桌面端
- **完整布局**: 所有功能展示
- **多列网格**: 3-4列布局
- **悬停效果**: 丰富交互

## 🔧 技术实现建议

### CSS框架
- 使用CSS变量系统
- 模块化组件样式
- 响应式媒体查询

### JavaScript交互
- 平滑滚动效果
- 图片懒加载
- 表单验证

### 性能优化
- 图片压缩优化
- CSS/JS文件压缩
- CDN加速

## ✅ 设计检查清单

### 视觉一致性
- [ ] 颜色使用规范
- [ ] 字体大小统一
- [ ] 间距系统一致
- [ ] 圆角规范统一

### 交互体验
- [ ] 按钮状态完整
- [ ] 表单验证友好
- [ ] 加载状态清晰
- [ ] 错误提示明确

### 响应式适配
- [ ] 移动端布局正确
- [ ] 平板端显示良好
- [ ] 桌面端功能完整
- [ ] 图片自适应

### 可访问性
- [ ] 颜色对比度达标
- [ ] 键盘导航支持
- [ ] 屏幕阅读器友好
- [ ] 语义化标签使用

---

**设计完成时间**: 2025年8月3日
**设计师**: Augment Agent
**版本**: v1.0
