#!/usr/bin/env node

/**
 * 产品分类更新脚本
 * 将"旋转式等离子表面处理机"从"大气等离子清洗机"移动到"小型真空等离子清洗机"
 */

const API_BASE = 'http://localhost:3000';

async function updateProductCategory() {
  try {
    console.log('🔄 开始更新产品分类...');
    
    // 1. 获取当前产品和分类信息
    console.log('📋 获取产品和分类信息...');
    const response = await fetch(`${API_BASE}/api/admin/products/update-category`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(`获取数据失败: ${data.error}`);
    }
    
    const { products, categories } = data.data;
    
    console.log('📊 当前数据状态:');
    console.log(`   产品数量: ${products.length}`);
    console.log(`   分类数量: ${categories.length}`);
    
    // 2. 查找目标产品
    const targetProduct = products.find(p => p.name === '旋转式等离子表面处理机');
    if (!targetProduct) {
      throw new Error('未找到"旋转式等离子表面处理机"产品');
    }
    
    console.log('🎯 找到目标产品:');
    console.log(`   ID: ${targetProduct.id}`);
    console.log(`   名称: ${targetProduct.name}`);
    console.log(`   当前分类: ${targetProduct.category_name || '未分类'} (${targetProduct.category_slug || 'N/A'})`);
    
    // 3. 查找目标分类
    const targetCategory = categories.find(c => c.slug === 'xxzkdlz');
    if (!targetCategory) {
      throw new Error('未找到"小型真空等离子清洗机"分类');
    }
    
    console.log('📁 找到目标分类:');
    console.log(`   ID: ${targetCategory.id}`);
    console.log(`   名称: ${targetCategory.name}`);
    console.log(`   Slug: ${targetCategory.slug}`);
    
    // 4. 检查是否需要更新
    if (targetProduct.category_id === targetCategory.id) {
      console.log('✅ 产品已经在正确的分类中，无需更新');
      return;
    }
    
    // 5. 执行更新
    console.log('🔄 执行分类更新...');
    const updateResponse = await fetch(`${API_BASE}/api/admin/products/update-category`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        productId: targetProduct.id,
        categoryId: targetCategory.id,
        categorySlug: targetCategory.slug
      })
    });
    
    const updateResult = await updateResponse.json();
    
    if (updateResult.success) {
      console.log('✅ 产品分类更新成功!');
      console.log('📦 更新后的产品信息:');
      console.log(`   产品: ${updateResult.data.name}`);
      console.log(`   新分类: ${updateResult.data.category_name}`);
      console.log(`   分类Slug: ${updateResult.data.category_slug}`);
    } else {
      throw new Error(`更新失败: ${updateResult.error}`);
    }
    
  } catch (error) {
    console.error('❌ 更新失败:', error.message);
    process.exit(1);
  }
}

// 验证服务器连接
async function checkServer() {
  try {
    const response = await fetch(`${API_BASE}/api/test-db`);
    if (!response.ok) {
      throw new Error(`服务器响应错误: ${response.status}`);
    }
    console.log('✅ 服务器连接正常');
    return true;
  } catch (error) {
    console.error('❌ 无法连接到服务器，请确保应用正在运行在 http://localhost:3000');
    console.error('错误详情:', error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🔧 产品分类更新工具');
  console.log('=' .repeat(60));
  
  // 检查服务器连接
  const serverOk = await checkServer();
  if (!serverOk) {
    process.exit(1);
  }

  // 执行更新
  await updateProductCategory();
  
  console.log('=' .repeat(60));
  console.log('🎉 操作完成!');
  console.log('💡 提示: 可以访问 /products/category/xxzkdlz 查看更新结果');
}

// 运行脚本
main().catch(error => {
  console.error('❌ 未处理的错误:', error);
  process.exit(1);
});
