import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 分类数据类型
interface Category {
  id?: number;
  name: string;
  slug: string;
  type: 'article' | 'product';
  description?: string;
  parent_id?: number;
  sort_order?: number;
}

// 生成URL slug的工具函数
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .trim()
    .substring(0, 50); // 限制长度
}

// GET - 获取分类列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'article' or 'product'
    const parentId = searchParams.get('parent_id');
    const withStats = searchParams.get('with_stats') === 'true';

    // 构建查询条件
    let whereClause = '';
    const queryParams: (string | number | null)[] = [];

    if (type) {
      whereClause = 'WHERE type = ?';
      queryParams.push(type);
    }

    if (parentId !== null) {
      if (whereClause) {
        whereClause += ' AND parent_id = ?';
      } else {
        whereClause = 'WHERE parent_id = ?';
      }
      queryParams.push(parentId || null);
    }

    let categoriesQuery;
    if (withStats && type === 'product') {
      // 包含产品统计信息
      categoriesQuery = `
        SELECT c.*, COUNT(p.id) as product_count
        FROM categories c
        LEFT JOIN demo_products p ON c.id = p.category_id AND p.is_active = 1
        ${whereClause}
        GROUP BY c.id, c.name, c.slug, c.type, c.description, c.parent_id, c.sort_order, c.created_at, c.updated_at
        ORDER BY c.sort_order ASC, c.name ASC
      `;
    } else {
      // 普通查询
      categoriesQuery = `
        SELECT * FROM categories 
        ${whereClause}
        ORDER BY sort_order ASC, name ASC
      `;
    }

    const categories = await query(categoriesQuery, queryParams);

    return NextResponse.json({
      success: true,
      data: categories
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST - 创建新分类
export async function POST(request: NextRequest) {
  try {
    const body: Category = await request.json();

    // 验证必填字段
    if (!body.name || !body.type) {
      return NextResponse.json(
        { success: false, error: 'Name and type are required' },
        { status: 400 }
      );
    }

    // 验证type值
    if (!['article', 'product'].includes(body.type)) {
      return NextResponse.json(
        { success: false, error: 'Type must be either "article" or "product"' },
        { status: 400 }
      );
    }

    // 自动生成slug（如果没有提供）
    const slug = body.slug || generateSlug(body.name);
    let finalSlug = slug;
    let counter = 1;

    // 检查slug是否已存在，如果存在则添加数字后缀
    while (true) {
      const existingCategory = await query(
        'SELECT id FROM categories WHERE slug = ?',
        [finalSlug]
      );

      if (!Array.isArray(existingCategory) || existingCategory.length === 0) {
        break;
      }

      finalSlug = `${slug}-${counter}`;
      counter++;
    }

    // 插入新分类
    const insertQuery = `
      INSERT INTO categories (
        name, slug, type, description, parent_id, sort_order
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = await query(insertQuery, [
      body.name,
      finalSlug,
      body.type,
      body.description || null,
      body.parent_id || null,
      body.sort_order || 0
    ]);

    return NextResponse.json({
      success: true,
      data: { 
        id: (result as unknown as { insertId: number }).insertId, 
        ...body, 
        slug: finalSlug 
      }
    });

  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create category' },
      { status: 500 }
    );
  }
}

// PUT - 更新分类
export async function PUT(request: NextRequest) {
  try {
    const body: Category & { id: number } = await request.json();

    if (!body.id) {
      return NextResponse.json(
        { success: false, error: 'Category ID is required' },
        { status: 400 }
      );
    }

    // 验证必填字段
    if (!body.name || !body.type) {
      return NextResponse.json(
        { success: false, error: 'Name and type are required' },
        { status: 400 }
      );
    }

    // 验证type值
    if (!['article', 'product'].includes(body.type)) {
      return NextResponse.json(
        { success: false, error: 'Type must be either "article" or "product"' },
        { status: 400 }
      );
    }

    // 生成新的slug（如果名称改变了）
    let finalSlug = body.slug || generateSlug(body.name);
    let counter = 1;

    // 检查新slug是否已被其他分类使用
    while (true) {
      const existingCategory = await query(
        'SELECT id FROM categories WHERE slug = ? AND id != ?',
        [finalSlug, body.id]
      );

      if (!Array.isArray(existingCategory) || existingCategory.length === 0) {
        break;
      }

      finalSlug = `${generateSlug(body.name)}-${counter}`;
      counter++;
    }

    // 更新分类
    const updateQuery = `
      UPDATE categories SET
        name = ?, slug = ?, type = ?, description = ?, parent_id = ?, 
        sort_order = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateQuery, [
      body.name,
      finalSlug,
      body.type,
      body.description || null,
      body.parent_id || null,
      body.sort_order || 0,
      body.id
    ]);

    return NextResponse.json({
      success: true,
      data: { ...body, slug: finalSlug }
    });

  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update category' },
      { status: 500 }
    );
  }
}

// DELETE - 删除分类
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Category ID is required' },
        { status: 400 }
      );
    }

    // 检查是否有产品使用此分类
    const productsUsingCategory = await query(
      'SELECT COUNT(*) as count FROM demo_products WHERE category_id = ?',
      [id]
    );

    if (Array.isArray(productsUsingCategory) && productsUsingCategory[0] && (productsUsingCategory[0] as { count: number }).count > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete category that is being used by products' },
        { status: 400 }
      );
    }

    // 检查是否有子分类
    const childCategories = await query(
      'SELECT COUNT(*) as count FROM categories WHERE parent_id = ?',
      [id]
    );

    if (Array.isArray(childCategories) && childCategories[0] && (childCategories[0] as { count: number }).count > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete category that has child categories' },
        { status: 400 }
      );
    }

    await query('DELETE FROM categories WHERE id = ?', [id]);

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete category' },
      { status: 500 }
    );
  }
}
