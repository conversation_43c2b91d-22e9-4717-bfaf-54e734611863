import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 创建数据库表的SQL语句
const CREATE_TABLES_SQL = [
  // 1. 创建分类表
  `CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('article', 'product') NOT NULL,
    description TEXT,
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_type (type),
    INDEX idx_sort_order (sort_order)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

  // 2. 插入初始分类数据
  `INSERT IGNORE INTO categories (name, slug, type, description, sort_order) VALUES
   ('等离子清洗机百科', 'dlzqxjbk', 'article', '等离子清洗机相关知识和技术文章', 1),
   ('等离子清洗机应用', 'dlzqxjyy', 'article', '等离子清洗机应用案例和解决方案', 2),
   ('小型真空等离子清洗机', 'xxzkdlz', 'product', '小型真空等离子清洗设备', 1),
   ('大型真空等离子清洗机', 'dxzkdlz', 'product', '大型真空等离子清洗设备', 2),
   ('大气等离子清洗机', 'dqdlz', 'product', '大气等离子清洗设备', 3),
   ('小型等离子清洗设备', 'small-plasma-equipment', 'product', '小型等离子清洗设备系列', 4)`
];

// 检查并添加字段的函数
async function addColumnIfNotExists(tableName: string, columnName: string, columnDefinition: string) {
  try {
    // 检查字段是否存在
    const columns = await query(`SHOW COLUMNS FROM ${tableName} LIKE '${columnName}'`);
    
    if (Array.isArray(columns) && columns.length === 0) {
      // 字段不存在，添加字段
      await query(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDefinition}`);
      return { status: 'added', column: columnName };
    } else {
      return { status: 'exists', column: columnName };
    }
  } catch (error) {
    return { status: 'error', column: columnName, error: error instanceof Error ? error.message : '未知错误' };
  }
}

export async function POST() {
  try {
    console.log('开始初始化数据库...');
    
    const results = [];
    
    // 1. 创建基础表结构
    for (const sql of CREATE_TABLES_SQL) {
      try {
        console.log('执行SQL:', sql.substring(0, 100) + '...');
        await query(sql);
        results.push({ sql: sql.substring(0, 100) + '...', status: 'success' });
      } catch (error) {
        console.error('SQL执行失败:', error);
        results.push({ sql: sql.substring(0, 100) + '...', status: 'error', error: error instanceof Error ? error.message : '未知错误' });
      }
    }

    // 2. 添加demo_products表的新字段
    const fieldsToAdd = [
      { name: 'features', definition: 'TEXT COMMENT \'产品特点\'' },
      { name: 'applications', definition: 'TEXT COMMENT \'应用领域\'' },
      { name: 'specifications', definition: 'TEXT COMMENT \'技术参数\'' },
      { name: 'category_id', definition: 'INT COMMENT \'分类ID\'' },
      { name: 'status', definition: 'ENUM(\'active\', \'inactive\', \'discontinued\') DEFAULT \'active\' COMMENT \'产品状态\'' },
      { name: 'seo_title', definition: 'VARCHAR(255) COMMENT \'SEO标题\'' },
      { name: 'seo_description', definition: 'TEXT COMMENT \'SEO描述\'' }
    ];

    console.log('添加demo_products表字段...');
    for (const field of fieldsToAdd) {
      const result = await addColumnIfNotExists('demo_products', field.name, field.definition);
      results.push(result);
    }

    // 3. 添加外键约束（如果不存在）
    try {
      await query(`ALTER TABLE demo_products ADD CONSTRAINT fk_demo_products_category 
                   FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL`);
      results.push({ constraint: 'fk_demo_products_category', status: 'added' });
    } catch (error) {
      // 外键可能已存在，这是正常的
      results.push({ constraint: 'fk_demo_products_category', status: 'exists_or_error', error: error instanceof Error ? error.message : '未知错误' });
    }

    // 4. 添加索引（如果不存在）
    try {
      await query(`ALTER TABLE demo_products ADD INDEX idx_demo_category_id (category_id)`);
      results.push({ index: 'idx_demo_category_id', status: 'added' });
    } catch (error) {
      results.push({ index: 'idx_demo_category_id', status: 'exists_or_error', error: error instanceof Error ? error.message : '未知错误' });
    }

    try {
      await query(`ALTER TABLE demo_products ADD INDEX idx_demo_status (status)`);
      results.push({ index: 'idx_demo_status', status: 'added' });
    } catch (error) {
      results.push({ index: 'idx_demo_status', status: 'exists_or_error', error: error instanceof Error ? error.message : '未知错误' });
    }

    // 5. 更新现有产品的分类关联
    try {
      const atmosphericCategoryResult = await query(`SELECT id FROM categories WHERE slug = 'dqdlz' AND type = 'product'`);
      if (Array.isArray(atmosphericCategoryResult) && atmosphericCategoryResult.length > 0) {
        const categoryId = (atmosphericCategoryResult[0] as { id: number }).id;
        await query(`UPDATE demo_products SET category_id = ?, status = 'active' WHERE category = 'atmospheric' AND category_id IS NULL`, [categoryId]);
        results.push({ update: 'atmospheric_products', status: 'success', categoryId });
      }
    } catch (error) {
      results.push({ update: 'atmospheric_products', status: 'error', error: error instanceof Error ? error.message : '未知错误' });
    }

    // 6. 更新产品详细信息
    try {
      await query(`UPDATE demo_products SET 
        features = ?,
        applications = ?,
        specifications = ?,
        seo_title = ?,
        seo_description = ?
        WHERE name = ? AND (features IS NULL OR features = '')`, [
        '• 低温不伤产品，产生中性等离子，不带静电\n• 增加表面活性，去除灰尘清除表面油污\n• 改变表面分子结构，精细清洗和去静电\n• 提高附着力亲水性，提升表面粘接的可靠性和持久性\n• 喷头可选，支持定制，气压可调节',
        '金属/光电子/汽车制造/纺织品/生物医疗/航天航空/电子/半导体/LED/塑胶等',
        '• 设备功率：1000W（可调）\n• 主机尺寸：420×202×427mm\n• 运行气压：0.25MPa\n• 输入电压：AC220V/50Hz\n• 处理速度：0.1M-0.5M/S\n• 机器重量：18KG\n• 处理宽度：≤100MM（可定制）\n• 输入气压：0.15-0.4MPa\n• 喷枪线缆长度：3000mm（可定制）\n• 处理高度：5-15mm\n\n喷头尺寸：\n常规款：20mm、32mm、45mm、50mm、56mm\n加大款：70mm、80mm、90mm、100mm（支持定制）',
        '旋转式等离子表面处理机 JC-DLZ-1000X - 专业等离子清洗设备',
        '旋转式等离子表面处理机JC-DLZ-1000X，适用于电子、汽车、航空等行业的表面处理，功率可调，支持定制。',
        '旋转式等离子表面处理机'
      ]);
      results.push({ update: 'product_details', status: 'success' });
    } catch (error) {
      results.push({ update: 'product_details', status: 'error', error: error instanceof Error ? error.message : '未知错误' });
    }

    // 7. 验证结果
    const tableInfo = await query(`DESCRIBE demo_products`);
    const categories = await query(`SELECT * FROM categories WHERE type = 'product' ORDER BY sort_order`);
    const products = await query(`SELECT id, name, category_id, features, applications, specifications FROM demo_products LIMIT 5`);

    console.log('数据库初始化完成');

    return NextResponse.json({
      success: true,
      message: '数据库初始化完成',
      results,
      verification: {
        tableStructure: tableInfo,
        categories,
        sampleProducts: products
      }
    });

  } catch (error) {
    console.error('数据库初始化失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '数据库初始化失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // 检查数据库状态
    const tableInfo = await query(`DESCRIBE demo_products`);
    const categories = await query(`SELECT * FROM categories WHERE type = 'product' ORDER BY sort_order`);
    const products = await query(`SELECT COUNT(*) as total FROM demo_products`);

    return NextResponse.json({
      success: true,
      status: {
        tableStructure: tableInfo,
        categoriesCount: categories.length,
        productsCount: products[0]?.total || 0,
        categories
      }
    });

  } catch (error) {
    console.error('检查数据库状态失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '检查数据库状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
