import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 数据库迁移脚本
const MIGRATION_QUERIES = [
  // 1. 检查表结构
  `SHOW COLUMNS FROM demo_products`,

  // 2. 插入新的产品分类（如果不存在）
  `INSERT IGNORE INTO categories (name, slug, type, description, sort_order) VALUES
   ('小型等离子清洗设备', 'small-plasma-equipment', 'product', '小型等离子清洗设备系列', 4)`
];

// 注意：ADD_COLUMN_QUERIES 已被内联到 POST 函数中，不再需要单独定义

// 更新产品数据的查询
const UPDATE_PRODUCT_QUERIES = [
  // 更新现有产品的分类关联
  `UPDATE demo_products p
   SET category_id = (SELECT id FROM categories WHERE slug = 'dqdlz' AND type = 'product'),
       status = 'active'
   WHERE p.category = 'atmospheric' AND p.category_id IS NULL`,

  // 更新产品详细信息
  `UPDATE demo_products SET 
    features = '• 低温不伤产品，产生中性等离子，不带静电\\n• 增加表面活性，去除灰尘清除表面油污\\n• 改变表面分子结构，精细清洗和去静电\\n• 提高附着力亲水性，提升表面粘接的可靠性和持久性\\n• 喷头可选，支持定制，气压可调节',
    applications = '金属/光电子/汽车制造/纺织品/生物医疗/航天航空/电子/半导体/LED/塑胶等',
    specifications = '• 设备功率：1000W（可调）\\n• 主机尺寸：420×202×427mm\\n• 运行气压：0.25MPa\\n• 输入电压：AC220V/50Hz\\n• 处理速度：0.1M-0.5M/S\\n• 机器重量：18KG\\n• 处理宽度：≤100MM（可定制）\\n• 输入气压：0.15-0.4MPa\\n• 喷枪线缆长度：3000mm（可定制）\\n• 处理高度：5-15mm\\n\\n喷头尺寸：\\n常规款：20mm、32mm、45mm、50mm、56mm\\n加大款：70mm、80mm、90mm、100mm（支持定制）',
    seo_title = '旋转式等离子表面处理机 JC-DLZ-1000X - 专业等离子清洗设备',
    seo_description = '旋转式等离子表面处理机JC-DLZ-1000X，适用于电子、汽车、航空等行业的表面处理，功率可调，支持定制。'
   WHERE name = '旋转式等离子表面处理机' AND (features IS NULL OR features = '')`
];

export async function POST() {
  try {
    console.log('开始执行数据库迁移...');

    const results = [];

    // 1. 检查当前表结构
    const currentColumns = await query(`SHOW COLUMNS FROM demo_products`);
    const existingColumns = currentColumns.map((col: any) => col.Field);

    console.log('当前表字段:', existingColumns);

    // 2. 添加缺失的字段
    const fieldsToAdd = [
      { name: 'features', sql: `ALTER TABLE demo_products ADD COLUMN features TEXT COMMENT '产品特点'` },
      { name: 'applications', sql: `ALTER TABLE demo_products ADD COLUMN applications TEXT COMMENT '应用领域'` },
      { name: 'specifications', sql: `ALTER TABLE demo_products ADD COLUMN specifications TEXT COMMENT '技术参数'` },
      { name: 'category_id', sql: `ALTER TABLE demo_products ADD COLUMN category_id INT COMMENT '分类ID'` },
      { name: 'status', sql: `ALTER TABLE demo_products ADD COLUMN status ENUM('active', 'inactive', 'discontinued') DEFAULT 'active' COMMENT '产品状态'` },
      { name: 'seo_title', sql: `ALTER TABLE demo_products ADD COLUMN seo_title VARCHAR(255) COMMENT 'SEO标题'` },
      { name: 'seo_description', sql: `ALTER TABLE demo_products ADD COLUMN seo_description TEXT COMMENT 'SEO描述'` }
    ];

    for (const field of fieldsToAdd) {
      if (!existingColumns.includes(field.name)) {
        try {
          console.log(`添加字段: ${field.name}`);
          await query(field.sql);
          results.push({ field: field.name, status: 'added' });
        } catch (error) {
          console.error(`添加字段 ${field.name} 失败:`, error);
          results.push({ field: field.name, status: 'error', error: error instanceof Error ? error.message : '未知错误' });
        }
      } else {
        results.push({ field: field.name, status: 'exists' });
      }
    }

    // 3. 执行其他迁移查询
    for (const sql of MIGRATION_QUERIES) {
      try {
        console.log('执行SQL:', sql.substring(0, 100) + '...');
        await query(sql);
        results.push({ sql: sql.substring(0, 100) + '...', status: 'success' });
      } catch (error) {
        console.error('SQL执行失败:', error);
        results.push({ sql: sql.substring(0, 100) + '...', status: 'error', error: error instanceof Error ? error.message : '未知错误' });
      }
    }

    // 执行数据更新
    for (const sql of UPDATE_PRODUCT_QUERIES) {
      try {
        console.log('执行数据更新:', sql.substring(0, 100) + '...');
        const result = await query(sql);
        results.push({ sql: sql.substring(0, 100) + '...', status: 'success', result });
      } catch (error) {
        console.error('数据更新失败:', error);
        results.push({ sql: sql.substring(0, 100) + '...', status: 'error', error: error instanceof Error ? error.message : '未知错误' });
      }
    }

    // 验证迁移结果
    const tableInfo = await query(`DESCRIBE demo_products`);
    const categories = await query(`SELECT * FROM categories WHERE type = 'product' ORDER BY sort_order`);
    const products = await query(`SELECT id, name, category_id, features, applications, specifications FROM demo_products LIMIT 5`);

    console.log('数据库迁移完成');

    return NextResponse.json({
      success: true,
      message: '数据库迁移完成',
      results,
      verification: {
        tableStructure: tableInfo,
        categories,
        sampleProducts: products
      }
    });

  } catch (error) {
    console.error('数据库迁移失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '数据库迁移失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // 检查迁移状态
    const tableInfo = await query(`DESCRIBE demo_products`);
    const hasNewFields = tableInfo.some((field: Record<string, unknown>) =>
      ['features', 'applications', 'specifications', 'category_id', 'status'].includes(field.Field as string)
    );

    const categories = await query(`SELECT * FROM categories WHERE type = 'product' ORDER BY sort_order`);
    const products = await query(`SELECT COUNT(*) as total FROM demo_products`);

    return NextResponse.json({
      success: true,
      migrationStatus: {
        hasNewFields,
        tableStructure: tableInfo,
        categoriesCount: categories.length,
        productsCount: (products[0] as { total?: number })?.total || 0
      }
    });

  } catch (error) {
    console.error('检查迁移状态失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '检查迁移状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
