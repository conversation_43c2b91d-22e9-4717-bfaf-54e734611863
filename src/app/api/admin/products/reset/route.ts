import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 新产品数据
const NEW_PRODUCT_DATA = {
  name: '旋转式等离子表面处理机',
  model: 'JC-DLZ-1000X',
  category: 'atmospheric',
  description: `旋转型等离子表面清洗机主要应用于电子行业的手机壳印刷、涂覆、点胶等前处理、手机屏幕的表面处理、国防工业的航空航天电连接器表面清洗、通用行业的丝网印刷、转移印刷前处理。

产品特点：
• 低温不伤产品，产生中性等离子，不带静电
• 增加表面活性，去除灰尘清除表面油污
• 改变表面分子结构，精细清洗和去静电
• 提高附着力亲水性，提升表面粘接的可靠性和持久性
• 喷头可选，支持定制，气压可调节

技术参数：
• 设备功率：1000W（可调）
• 主机尺寸：420×202×427mm
• 运行气压：0.25MPa
• 输入电压：AC220V/50Hz
• 处理速度：0.1M-0.5M/S
• 机器重量：18KG
• 处理宽度：≤100MM（可定制）
• 输入气压：0.15-0.4MPa
• 喷枪线缆长度：3000mm（可定制）
• 处理高度：5-15mm

喷头尺寸：
常规款：20mm、32mm、45mm、50mm、56mm
加大款：70mm、80mm、90mm、100mm（支持定制）

应用领域：
金属/光电子/汽车制造/纺织品/生物医疗/航天航空/电子/半导体/LED/塑胶等`,
  image: '/images/products/jc-dlz-1000x.jpg',
  sort_order: 1,
  is_active: true
};

// 生成URL slug的工具函数
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .trim()
    .substring(0, 50); // 限制长度
}

// POST - 重置产品数据
export async function POST() {
  try {
    console.log('开始重置产品数据...');

    // 1. 清除所有现有产品数据
    console.log('清除现有产品数据...');
    await query('DELETE FROM demo_products');
    console.log('现有产品数据已清除');

    // 2. 生成新产品的slug和href
    const slug = generateSlug(NEW_PRODUCT_DATA.name);
    const href = `/products/${slug}`;

    // 3. 插入新产品数据
    console.log('插入新产品数据...');
    const insertQuery = `
      INSERT INTO demo_products (
        name, model, slug, category, description, href, image, sort_order, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await query(insertQuery, [
      NEW_PRODUCT_DATA.name,
      NEW_PRODUCT_DATA.model,
      slug,
      NEW_PRODUCT_DATA.category,
      NEW_PRODUCT_DATA.description,
      href,
      NEW_PRODUCT_DATA.image,
      NEW_PRODUCT_DATA.sort_order,
      NEW_PRODUCT_DATA.is_active ? 1 : 0
    ]);

    const insertId = (result as unknown as { insertId: number }).insertId;
    console.log(`新产品已插入，ID: ${insertId}`);

    return NextResponse.json({
      success: true,
      message: '产品数据重置成功',
      data: {
        id: insertId,
        ...NEW_PRODUCT_DATA,
        slug,
        href
      }
    });

  } catch (error) {
    console.error('重置产品数据失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '重置产品数据失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// GET - 获取当前产品数据状态
export async function GET() {
  try {
    const products = await query('SELECT * FROM demo_products ORDER BY sort_order ASC, created_at DESC');
    
    return NextResponse.json({
      success: true,
      data: products,
      count: products.length
    });

  } catch (error) {
    console.error('获取产品数据失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '获取产品数据失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
