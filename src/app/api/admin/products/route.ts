import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 产品数据类型
interface Product {
  id?: number;
  name: string;
  model?: string;
  slug: string;
  category?: string;
  category_id?: number;
  description?: string;
  features?: string;
  applications?: string;
  specifications?: string;
  href?: string;
  image?: string;
  sort_order?: number;
  status?: 'active' | 'inactive' | 'discontinued';
  seo_title?: string;
  seo_description?: string;
  is_active?: boolean;
}

// GET - 获取产品列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const status = searchParams.get('status') || '';

    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: (string | number | boolean)[] = [];

    if (search) {
      whereClause += ' AND (name LIKE ? OR model LIKE ? OR description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (category) {
      whereClause += ' AND category = ?';
      params.push(category);
    }

    if (status !== '') {
      whereClause += ' AND is_active = ?';
      params.push(status === 'true');
    }

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM demo_products ${whereClause}`;
    const [countResult] = await query(countQuery, params);
    const total = (countResult as { total: number }).total;

    // 获取产品列表（包含分类信息）
    const productsQuery = `
      SELECT p.*, c.name as category_name, c.slug as category_slug
      FROM demo_products p
      LEFT JOIN categories c ON p.category_id = c.id
      ${whereClause}
      ORDER BY p.sort_order ASC, p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const queryParams = [...params, limit, offset];
    const products = await query(productsQuery, queryParams);

    return NextResponse.json({
      success: true,
      data: {
        products,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// 生成URL slug的工具函数
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .trim()
    .substring(0, 50); // 限制长度
}

// POST - 创建新产品
export async function POST(request: NextRequest) {
  try {
    const body: Product = await request.json();

    // 验证必填字段
    if (!body.name) {
      return NextResponse.json(
        { success: false, error: 'Name is required' },
        { status: 400 }
      );
    }

    // 自动生成slug和href
    const slug = generateSlug(body.name);
    let finalSlug = slug;
    let counter = 1;

    // 检查slug是否已存在，如果存在则添加数字后缀
    while (true) {
      const existingProduct = await query(
        'SELECT id FROM demo_products WHERE slug = ?',
        [finalSlug]
      );

      if (!Array.isArray(existingProduct) || existingProduct.length === 0) {
        break;
      }

      finalSlug = `${slug}-${counter}`;
      counter++;
    }

    const finalHref = `/products/${finalSlug}`;

    // 插入新产品
    const insertQuery = `
      INSERT INTO demo_products (
        name, model, slug, category, category_id, description, features, applications,
        specifications, href, image, sort_order, status, seo_title, seo_description, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await query(insertQuery, [
      body.name,
      body.model || null,
      finalSlug,
      body.category || null,
      body.category_id || null,
      body.description || null,
      body.features || null,
      body.applications || null,
      body.specifications || null,
      finalHref,
      body.image || null,
      body.sort_order || 0,
      body.status || 'active',
      body.seo_title || null,
      body.seo_description || null,
      body.is_active !== false ? 1 : 0
    ]);

    return NextResponse.json({
      success: true,
      data: { id: (result as unknown as { insertId: number }).insertId, ...body, slug: finalSlug, href: finalHref }
    });

  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create product' },
      { status: 500 }
    );
  }
}

// PUT - 更新产品
export async function PUT(request: NextRequest) {
  try {
    const body: Product & { id: number } = await request.json();

    if (!body.id) {
      return NextResponse.json(
        { success: false, error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // 验证必填字段
    if (!body.name) {
      return NextResponse.json(
        { success: false, error: 'Name is required' },
        { status: 400 }
      );
    }

    // 获取当前产品信息
    const currentProduct = await query(
      'SELECT slug, href FROM demo_products WHERE id = ?',
      [body.id]
    );

    let finalSlug = '';
    let finalHref = '';

    if (Array.isArray(currentProduct) && currentProduct.length > 0) {
      // 如果名称改变了，重新生成slug和href
      const slug = generateSlug(body.name);
      finalSlug = slug;
      let counter = 1;

      // 检查新slug是否已被其他产品使用
      while (true) {
        const existingProduct = await query(
          'SELECT id FROM demo_products WHERE slug = ? AND id != ?',
          [finalSlug, body.id]
        );

        if (!Array.isArray(existingProduct) || existingProduct.length === 0) {
          break;
        }

        finalSlug = `${slug}-${counter}`;
        counter++;
      }

      finalHref = `/products/${finalSlug}`;
    }

    // 更新产品
    const updateQuery = `
      UPDATE demo_products SET
        name = ?, model = ?, slug = ?, category = ?, category_id = ?, description = ?,
        features = ?, applications = ?, specifications = ?, href = ?, image = ?,
        sort_order = ?, status = ?, seo_title = ?, seo_description = ?,
        is_active = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateQuery, [
      body.name,
      body.model || null,
      finalSlug,
      body.category || null,
      body.category_id || null,
      body.description || null,
      body.features || null,
      body.applications || null,
      body.specifications || null,
      finalHref,
      body.image || null,
      body.sort_order || 0,
      body.status || 'active',
      body.seo_title || null,
      body.seo_description || null,
      body.is_active !== false ? 1 : 0,
      body.id
    ]);

    return NextResponse.json({
      success: true,
      data: { ...body, slug: finalSlug, href: finalHref }
    });

  } catch (error) {
    console.error('Error updating product:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

// DELETE - 删除产品
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Product ID is required' },
        { status: 400 }
      );
    }

    await query('DELETE FROM demo_products WHERE id = ?', [id]);

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}
