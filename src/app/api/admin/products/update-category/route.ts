import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// POST - 更新产品分类
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { productId, categoryId, categorySlug } = body;

    console.log('更新产品分类:', { productId, categoryId, categorySlug });

    // 验证参数
    if (!productId) {
      return NextResponse.json(
        { success: false, error: '产品ID是必需的' },
        { status: 400 }
      );
    }

    // 如果提供了categorySlug，先获取对应的categoryId
    let finalCategoryId = categoryId;
    if (categorySlug && !categoryId) {
      const categoryResult = await query(
        `SELECT id FROM categories WHERE slug = ? AND type = 'product'`,
        [categorySlug]
      );
      
      if (Array.isArray(categoryResult) && categoryResult.length > 0) {
        finalCategoryId = (categoryResult[0] as { id: number }).id;
      } else {
        return NextResponse.json(
          { success: false, error: `找不到slug为"${categorySlug}"的分类` },
          { status: 404 }
        );
      }
    }

    // 更新产品的分类
    const updateQuery = `
      UPDATE demo_products 
      SET category_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateQuery, [finalCategoryId, productId]);

    // 获取更新后的产品信息
    const updatedProduct = await query(
      `SELECT p.*, c.name as category_name, c.slug as category_slug 
       FROM demo_products p 
       LEFT JOIN categories c ON p.category_id = c.id 
       WHERE p.id = ?`,
      [productId]
    );

    return NextResponse.json({
      success: true,
      message: '产品分类更新成功',
      data: updatedProduct[0]
    });

  } catch (error) {
    console.error('更新产品分类失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '更新产品分类失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// GET - 获取产品和分类信息
export async function GET() {
  try {
    // 获取所有产品及其分类信息
    const products = await query(`
      SELECT p.*, c.name as category_name, c.slug as category_slug 
      FROM demo_products p 
      LEFT JOIN categories c ON p.category_id = c.id 
      ORDER BY p.sort_order ASC, p.created_at DESC
    `);

    // 获取所有产品分类
    const categories = await query(`
      SELECT * FROM categories 
      WHERE type = 'product' 
      ORDER BY sort_order ASC
    `);

    return NextResponse.json({
      success: true,
      data: {
        products,
        categories
      }
    });

  } catch (error) {
    console.error('获取产品和分类信息失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '获取产品和分类信息失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
