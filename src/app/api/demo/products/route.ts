import { NextRequest, NextResponse } from 'next/server';

// 演示用产品数据 - 从页面组件迁移到API
const DEMO_PRODUCTS_DATA = [
  {
    id: 1,
    name: '小型等离子清洗机',
    model: 'PM-20LN',
    slug: 'small-vacuum',
    category: 'vacuum',
    description: '适用于小批量样品处理的真空等离子清洗设备',
    href: '/products/small-vacuum',
    image: '/images/products/pm-20ln.jpg'
  },
  {
    id: 2,
    name: '大型等离子清洗机',
    model: 'PM-2300LNR60LN',
    slug: 'large-vacuum',
    category: 'vacuum',
    description: '适用于大批量生产的真空等离子清洗设备',
    href: '/products/large-vacuum',
    image: '/images/products/pm-2300.jpg'
  },
  {
    id: 3,
    name: '医疗导管等离子清洗机',
    model: 'PM-210LN',
    slug: 'medical-catheter',
    category: 'medical',
    description: '专为医疗导管等医疗器械设计的等离子清洗设备',
    href: '/products/large-vacuum',
    image: '/images/products/pm-210ln.jpg'
  },
  {
    id: 4,
    name: '等离子干刻机',
    model: 'JY-36LN',
    slug: 'dry-etching',
    category: 'etching',
    description: '专业的等离子干刻设备，适用于微电子加工',
    href: '/products/large-vacuum',
    image: '/images/products/jy-36ln.jpg'
  },
  {
    id: 5,
    name: '小型等离子清洗机',
    model: 'PM-3LN',
    slug: 'mini-vacuum',
    category: 'vacuum',
    description: '超小型等离子清洗设备，适合桌面使用',
    href: '/products/small-vacuum',
    image: '/images/products/pm-3ln.jpg'
  },
  {
    id: 6,
    name: '大气等离子清洗设备',
    model: 'AP-PM1000',
    slug: 'atmospheric',
    category: 'atmospheric',
    description: '常压下工作的等离子清洗设备，无需真空系统',
    href: '/products/atmospheric',
    image: '/images/products/ap-pm1000.jpg'
  },
  {
    id: 7,
    name: '薄膜等离子清洗机',
    model: 'AP-800-AJR',
    slug: 'film-atmospheric',
    category: 'atmospheric',
    description: '专为薄膜材料设计的大气等离子清洗设备',
    href: '/products/atmospheric',
    image: '/images/products/ap-800.jpg'
  },
  {
    id: 8,
    name: '真空等离子清洗机',
    model: 'PM/R-80L',
    slug: 'vacuum-80l',
    category: 'vacuum',
    description: '大容量真空等离子清洗设备，适用于批量处理',
    href: '/products/large-vacuum',
    image: '/images/products/pm-r-80l.jpg'
  }
];

// 新闻文章数据
const NEWS_ARTICLES_DATA = [
  {
    id: 1,
    title: '微流控PDMS芯片键合等离子清洗机应用-微流控pdms芯片键',
    date: '2025-07-24',
    excerpt: '微流控PDMS芯片键合等离子清洗机是一种用于清洗微流控PDMS芯片的设备，该设备在微流控技术和等离子清洗技术的...',
    href: '/news/applications',
    category: 'applications'
  },
  {
    id: 2,
    title: '等离子外加工清洗机、等离子外加工清洗机：高效清洗新选择',
    date: '2025-07-24',
    excerpt: '随着现代工业的不断发展，清洗工艺也得到了越来越多的关注。传统的清洗方法可能存在对环境的污染、清洗效率低下等问题...',
    href: '/news/applications',
    category: 'applications'
  },
  {
    id: 3,
    title: '改善聚丙烯腈PAN润湿性和粘接性 改善材料表面的性能(改善聚',
    date: '2025-07-24',
    excerpt: '随着科学技术的不断发展，材料科学领域也在不断进步。在材料研究中，表面性能的改善一直是研究的重点之一。本文将围绕...',
    href: '/news/applications',
    category: 'applications'
  },
  {
    id: 4,
    title: '等离子除胶处理机使用方法—等离子除胶处理机使用指南',
    date: '2025-07-24',
    excerpt: '等离子除胶处理机使用指南你是否曾经遇到过这样的问题：胶水在工业生产过程中难以去除，导致产品质量下降，甚至无法正...',
    href: '/news/applications',
    category: 'applications'
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'products'; // products | news
    const limit = searchParams.get('limit');
    const category = searchParams.get('category');

    interface DataItem {
      category?: string;
      [key: string]: unknown;
    }

    let data: DataItem[] =
      type === 'news' ? NEWS_ARTICLES_DATA : DEMO_PRODUCTS_DATA;

    // 按类别过滤
    if (category) {
      data = data.filter((item: DataItem) => item.category === category);
    }

    // 限制返回数量
    if (limit) {
      const limitNum = parseInt(limit);
      data = data.slice(0, limitNum);
    }

    return NextResponse.json({
      data,
      total: data.length,
      type
    });

  } catch (error) {
    console.error('Error fetching demo products:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    // 预留接口用于未来的数据更新
    // const body = await request.json();

    return NextResponse.json({
      success: true,
      message: 'Demo products data updated successfully'
    });

  } catch (error) {
    console.error('Error updating demo products:', error);
    return NextResponse.json(
      { error: 'Failed to update demo products data' },
      { status: 500 }
    );
  }
}
