import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params;
  try {
    const imagePath = resolvedParams.path.join('/');

    // 构建图片文件路径
    const filePath = join(process.cwd(), 'public', 'images', imagePath);

    // 检查文件是否存在
    if (!existsSync(filePath)) {
      return new NextResponse('Image not found', { status: 404 });
    }

    // 读取文件
    const imageBuffer = await readFile(filePath);

    // 获取文件扩展名来确定MIME类型
    const ext = imagePath.split('.').pop()?.toLowerCase();
    let contentType = 'image/jpeg'; // 默认类型

    switch (ext) {
      case 'png':
        contentType = 'image/png';
        break;
      case 'gif':
        contentType = 'image/gif';
        break;
      case 'webp':
        contentType = 'image/webp';
        break;
      case 'svg':
        contentType = 'image/svg+xml';
        break;
      case 'jpg':
      case 'jpeg':
      default:
        contentType = 'image/jpeg';
        break;
    }

    // 返回图片
    return new NextResponse(imageBuffer as unknown as BodyInit, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable',
        'Content-Length': imageBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Error serving image:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
