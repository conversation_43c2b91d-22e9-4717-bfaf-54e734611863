@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary: #64748b;
  --accent: #0ea5e9;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --text-muted: #64748b;
}

/* Clean & Simple Dropdown Menu Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out forwards;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f8fafc;
    --border: #334155;
    --border-light: #1e293b;
    --text-muted: #94a3b8;
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Global Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-padding {
  padding: 4rem 0;
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-height: 36px;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--primary);
  padding: 8px 12px;
  border: 2px solid var(--primary);
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-height: 36px;
}

.btn-secondary:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-1px);
}

/* Antd integration - 遵循Ant Design标准设计 */
.ant-card {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-card:hover {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 2px 8px -1px rgba(0, 0, 0, 0.05), 0 4px 4px 0 rgba(0, 0, 0, 0.02);
}

.ant-card .ant-card-body {
  padding: 24px;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 400;
  padding: 4px 15px;
  height: 32px;
  font-size: 14px;
  line-height: 1.5715;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

/* 移除自定义input样式，使用Ant Design默认样式 */
.ant-input {
  /* 使用Ant Design默认样式 */
}

.ant-input:focus {
  /* 使用Ant Design默认样式 */
}

.ant-select-selector {
  /* 使用Ant Design默认样式 */
}

.ant-select-selector:hover {
  /* 使用Ant Design默认样式 */
}

.ant-modal-content {
  border-radius: 6px;
}

.ant-modal-header {
  border-radius: 6px 6px 0 0;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-body {
  padding: 24px;
}

.ant-modal-footer {
  padding: 10px 16px;
  border-top: 1px solid #f0f0f0;
}

/* Demo specific styles */
.plasma-demo-wrapper .demo-content {
  min-height: 500px;
}

.plasma-demo-wrapper .control-panel,
.plasma-demo-wrapper .info-panel {
  height: fit-content;
}

.plasma-demo-wrapper .scene-container {
  min-height: 400px;
}

/* Enhanced Header Animations */
.header-hidden {
  transform: translateY(-100%);
  opacity: 0;
}

.header-visible {
  transform: translateY(0);
  opacity: 1;
}

.logo-container {
  position: relative;
  overflow: hidden;
}

.logo-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.logo-container:hover::before {
  left: 100%;
}

/* Navigation Animations */
.nav-item {
  animation: slideInDown 0.6s ease-out both;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-link {
  border-radius: 0.5rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
  min-width: 120px;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.2;
}

.nav-link:hover .nav-link-bg {
  transform: scale-x-1;
}

.nav-link:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Enhanced Dropdown Animations */
.dropdown-menu {
  border: 1px solid rgba(59, 130, 246, 0.08);
  position: absolute;
  min-width: 200px;
}

.dropdown-menu::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 12px;
  height: 12px;
  background: white;
  border: 1px solid rgba(59, 130, 246, 0.08);
  border-bottom: none;
  border-right: none;
  z-index: -1;
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
}

.dropdown-item {
  position: relative;
  margin: 0;
  background: white;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  text-align: left;
  width: 100%;
  display: block;
}

.dropdown-item:first-child {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.dropdown-item:last-child {
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
  border-bottom: none;
}

.dropdown-item:hover {
  transform: translateX(4px);
  box-shadow: inset 3px 0 0 rgba(59, 130, 246, 0.4);
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05), rgba(99, 102, 241, 0.05));
}

/* Keyframe Animations */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
  66% {
    transform: translateY(-10px) translateX(-5px);
    opacity: 0.8;
  }
}

/* Header Styles */
body {
  padding-top: 64px; /* 为固定header留出空间 */
}

/* Enhanced Hero Banner Styles */
.hero-banner {
  min-height: 100vh;
  background: linear-gradient(-45deg, #2563eb, #3b82f6, #1d4ed8, #1e40af);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  position: relative;
  margin-top: -64px; /* 抵消body的padding-top */
  padding-top: 64px; /* 确保内容不被header遮挡 */
}

/* Scroll Animation Styles */
.scroll-section {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-section.animate-in-view {
  opacity: 1;
  transform: translateY(0);
}

.feature-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-section.animate-in-view .feature-card {
  opacity: 1;
  transform: translateY(0);
}

.product-card {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-section.animate-in-view .product-card {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.contact-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-section.animate-in-view .contact-card {
  opacity: 1;
  transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: scale(1.1);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Progress bar for scroll */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8, #8b5cf6);
  transform-origin: left;
  z-index: 9999;
  transition: transform 0.1s ease-out;
}

.animate-gradient {
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
}

/* Floating Particles */
.particles-container {
  pointer-events: none;
}

.particle {
  position: absolute;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  border-radius: 50%;
  animation: particleFloat 6s ease-in-out infinite;
}

.particle-1 {
  width: 4px;
  height: 4px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 8s;
}

.particle-2 {
  width: 6px;
  height: 6px;
  top: 60%;
  left: 20%;
  animation-delay: 2s;
  animation-duration: 10s;
}

.particle-3 {
  width: 3px;
  height: 3px;
  top: 30%;
  left: 80%;
  animation-delay: 4s;
  animation-duration: 7s;
}

.particle-4 {
  width: 5px;
  height: 5px;
  top: 80%;
  left: 70%;
  animation-delay: 1s;
  animation-duration: 9s;
}

.particle-5 {
  width: 4px;
  height: 4px;
  top: 50%;
  left: 90%;
  animation-delay: 3s;
  animation-duration: 6s;
}

.particle-6 {
  width: 7px;
  height: 7px;
  top: 10%;
  left: 60%;
  animation-delay: 5s;
  animation-duration: 11s;
}

/* Hero Content Animations */
.hero-badge {
  position: relative;
  overflow: hidden;
}

.hero-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.hero-badge:hover::before {
  left: 100%;
}

.hero-title-main {
  background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-title-accent {
  background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer 3s ease-in-out infinite;
}

.animate-shimmer {
  background-size: 200% auto;
  animation: shimmer 2s linear infinite;
}

/* Enhanced Button Styles */
.hero-btn {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hero-btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.hero-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.hero-btn:hover::after {
  left: 100%;
}

/* Decorative Elements */
.decorative-element {
  animation: float 6s ease-in-out infinite;
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out both;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .plasma-demo-wrapper .demo-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .plasma-demo-wrapper .scene-container {
    order: -1;
  }

  .dropdown-menu {
    width: 220px;
    left: 50%;
    transform: translateX(-50%);
  }

  .nav-link {
    min-width: 100px;
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
  }

  .hero-banner {
    min-height: 80vh;
    padding: 2rem 0;
  }

  .particle {
    display: none;
  }
}

@media (max-width: 768px) {
  .dropdown-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    width: 100%;
    border-radius: 0;
    border-left: none;
    border-right: none;
    max-height: 50vh;
    overflow-y: auto;
    transform: none;
  }

  .dropdown-menu::before {
    display: none;
  }

  .hero-banner {
    min-height: 70vh;
    text-align: center;
  }

  .hero-btn {
    width: 100%;
    justify-content: center;
  }

  .decorative-element {
    display: none;
  }
}

@media (max-width: 480px) {
  .nav-item {
    animation-delay: 0s !important;
  }

  .hero-title-main {
    font-size: 2rem;
  }

  .hero-title-accent {
    font-size: 1.5rem;
  }
}

/* Accessibility: Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .nav-item,
  .dropdown-menu,
  .dropdown-item,
  .nav-link,
  .logo-container::before,
  .hero-banner,
  .animate-gradient,
  .particle,
  .hero-badge::before,
  .hero-title-accent,
  .hero-btn::after,
  .decorative-element,
  .animate-fadeInUp {
    animation: none !important;
    transition: opacity 0.2s ease !important;
  }

  .nav-link:hover,
  .dropdown-item:hover,
  .hero-btn:hover {
    transform: none !important;
  }

  .hero-banner {
    background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
  }
}

/* 导航菜单优化样式 */

/* 导航链接hover效果 */
.nav-link:hover .nav-link-bg {
  transform: scaleX(1);
}

/* 下拉菜单项优化 */
.dropdown-item {
  position: relative;
  overflow: hidden;
}

.dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.dropdown-item:hover::before {
  left: 100%;
}

/* 改善触摸设备体验 */
@media (hover: none) and (pointer: coarse) {
  .dropdown-item:hover::before {
    display: none;
  }

  .dropdown-item:active {
    background-color: rgb(239, 246, 255);
    transform: scale(0.98);
    transition: all 0.1s ease;
  }
}

/* 确保下拉菜单在移动端的可访问性 */
@media (max-width: 1023px) {
  .dropdown-menu {
    position: fixed !important;
    top: auto !important;
    left: 1rem !important;
    right: 1rem !important;
    transform: none !important;
    width: auto !important;
    margin-top: 0.5rem;
    max-height: 70vh;
    overflow-y: auto;
  }

  .dropdown-menu .absolute.-top-2 {
    display: none;
  }

  .dropdown-menu .absolute.-top-1 {
    display: none;
  }
}

/* 改善滚动条样式 */
.dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 增强焦点可见性 */
.nav-link:focus,
.dropdown-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 减少动画对于偏好减少动画的用户 */
@media (prefers-reduced-motion: reduce) {
  .nav-link,
  .dropdown-item,
  .animate-fadeIn {
    animation: none !important;
    transition: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .dropdown-menu {
    border: 2px solid #000;
  }

  .dropdown-item:hover {
    background-color: #000;
    color: #fff;
  }
}
