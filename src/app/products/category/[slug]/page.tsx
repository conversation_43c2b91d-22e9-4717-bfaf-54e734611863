import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { query } from '@/lib/db';

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
}

interface Product {
  id: number;
  name: string;
  model?: string;
  slug: string;
  description?: string;
  features?: string;
  applications?: string;
  specifications?: string;
  image?: string;
  href?: string;
  sort_order: number;
  seo_title?: string;
  seo_description?: string;
}

interface CategoryPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// 获取分类信息
async function getCategory(slug: string): Promise<Category | null> {
  try {
    const categories = await query(
      `SELECT * FROM categories WHERE slug = ? AND type = 'product'`,
      [slug]
    );
    
    if (Array.isArray(categories) && categories.length > 0) {
      return categories[0] as unknown as Category;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching category:', error);
    return null;
  }
}

// 获取分类下的产品
async function getCategoryProducts(categoryId: number): Promise<Product[]> {
  try {
    const products = await query(
      `SELECT * FROM demo_products 
       WHERE category_id = ? AND is_active = 1 
       ORDER BY sort_order ASC, created_at DESC`,
      [categoryId]
    );
    
    return Array.isArray(products) ? products as unknown as Product[] : [];
  } catch (error) {
    console.error('Error fetching category products:', error);
    return [];
  }
}

// 生成页面元数据
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug } = await params;
  const category = await getCategory(slug);
  
  if (!category) {
    return {
      title: '分类未找到',
      description: '请求的产品分类不存在'
    };
  }

  return {
    title: `${category.name} - 等离子清洗设备`,
    description: category.description || `${category.name}系列等离子清洗设备`,
    keywords: `${category.name}, 等离子清洗机, 等离子表面处理`,
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug } = await params;
  const category = await getCategory(slug);
  
  if (!category) {
    notFound();
  }

  const products = await getCategoryProducts(category.id);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 面包屑导航 */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-2 py-4 text-sm">
            <span className="text-gray-500">您当前的位置:</span>
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              首页
            </Link>
            <span className="text-gray-400">&gt;</span>
            <Link href="/products" className="text-blue-600 hover:text-blue-800">
              设备推荐
            </Link>
            <span className="text-gray-400">&gt;</span>
            <span className="text-gray-900">{category.name}</span>
          </div>
        </div>
      </nav>

      {/* 页面头部 */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {category.name}
            </h1>
            {category.description && (
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {category.description}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* 产品列表 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {products.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg mb-4">
              该分类下暂无产品
            </div>
            <Link
              href="/products"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              查看全部产品
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.map((product) => (
              <div
                key={product.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
              >
                {/* 产品图片 */}
                <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                  {product.image ? (
                    <Image
                      src={product.image}
                      alt={product.name}
                      width={400}
                      height={225}
                      className="w-full h-48 object-cover"
                    />
                  ) : (
                    <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-400">暂无图片</span>
                    </div>
                  )}
                </div>

                {/* 产品信息 */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {product.name}
                  </h3>
                  
                  {product.model && (
                    <p className="text-sm text-gray-600 mb-2">
                      型号：{product.model}
                    </p>
                  )}

                  {product.description && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {product.description}
                    </p>
                  )}

                  {/* 产品特点 */}
                  {product.features && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">产品特点：</h4>
                      <div className="text-sm text-gray-600 line-clamp-3">
                        {product.features.split('\n').slice(0, 3).map((feature, index) => (
                          <div key={index}>{feature}</div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 应用领域 */}
                  {product.applications && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">应用领域：</h4>
                      <p className="text-sm text-gray-600">{product.applications}</p>
                    </div>
                  )}

                  {/* 操作按钮 */}
                  <div className="flex space-x-3">
                    <Link
                      href={product.href || `/products/${product.slug}`}
                      className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200"
                    >
                      了解详情
                    </Link>
                    <Link
                      href="/demo"
                      className="flex-1 bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-md hover:bg-gray-200 transition-colors duration-200"
                    >
                      3D演示
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 返回产品列表 */}
        <div className="text-center mt-12">
          <Link
            href="/products"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <svg className="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            查看全部产品
          </Link>
        </div>
      </div>
    </div>
  );
}
