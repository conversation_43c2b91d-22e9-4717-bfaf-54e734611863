'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';

interface Category {
  id: number;
  name: string;
  slug: string;
  type: 'article' | 'product';
  description?: string;
  parent_id?: number;
  sort_order: number;
  product_count?: number;
}

interface DynamicNavigationProps {
  className?: string;
}

export default function DynamicNavigation({ className = '' }: DynamicNavigationProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isDropdownHovered, setIsDropdownHovered] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories?type=product&with_stats=true');
      const data = await response.json();

      if (data.success) {
        setCategories(data.data);
      } else {
        setError('获取分类失败');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setError('获取分类失败');
    } finally {
      setLoading(false);
    }
  };

  // 清除定时器的函数
  const clearHideTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 鼠标进入主菜单项
  const handleMouseEnter = useCallback((slug: string) => {
    clearHideTimeout();
    setActiveDropdown(slug);
  }, [clearHideTimeout]);

  // 鼠标离开主菜单项
  const handleMouseLeave = useCallback(() => {
    // 延迟关闭，给用户时间移动到下拉菜单
    timeoutRef.current = setTimeout(() => {
      if (!isDropdownHovered) {
        setActiveDropdown(null);
      }
    }, 150); // 150ms延迟
  }, [isDropdownHovered]);

  // 鼠标进入下拉菜单
  const handleDropdownMouseEnter = useCallback(() => {
    clearHideTimeout();
    setIsDropdownHovered(true);
  }, [clearHideTimeout]);

  // 鼠标离开下拉菜单
  const handleDropdownMouseLeave = useCallback(() => {
    setIsDropdownHovered(false);
    timeoutRef.current = setTimeout(() => {
      setActiveDropdown(null);
    }, 100); // 稍短的延迟
  }, []);

  // 处理移动端点击
  const handleMobileToggle = useCallback((slug: string, e: React.MouseEvent) => {
    e.preventDefault();
    if (activeDropdown === slug) {
      setActiveDropdown(null);
    } else {
      setActiveDropdown(slug);
    }
  }, [activeDropdown]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    };

    if (activeDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown]);

  // 键盘导航支持
  const handleKeyDown = useCallback((e: React.KeyboardEvent, slug: string) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      if (activeDropdown === slug) {
        setActiveDropdown(null);
      } else {
        setActiveDropdown(slug);
      }
    } else if (e.key === 'Escape') {
      setActiveDropdown(null);
    }
  }, [activeDropdown]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  if (loading) {
    return (
      <nav className={className}>
        <div className="flex space-x-8">
          <div className="h-6 w-20 bg-gray-200 animate-pulse rounded"></div>
          <div className="h-6 w-20 bg-gray-200 animate-pulse rounded"></div>
          <div className="h-6 w-20 bg-gray-200 animate-pulse rounded"></div>
        </div>
      </nav>
    );
  }

  if (error) {
    return (
      <nav className={className}>
        <div className="text-red-500 text-sm">{error}</div>
      </nav>
    );
  }

  // 静态导航项
  const staticNavItems = [
    { label: '网站首页', href: '/' },
    { label: '关于我们', href: '/about' },
  ];

  // 动态产品分类导航项
  const productCategories = categories.filter(cat => cat.type === 'product');

  // 静态导航项（其他）
  const otherNavItems = [
    { label: '新闻中心', href: '/news' },
    { label: '3D演示', href: '/demo' },
    { label: '联系我们', href: '/contact-us' },
  ];

  return (
    <nav className={className}>
      <div className="flex items-center space-x-1">
        {/* 静态导航项 */}
        {staticNavItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className="nav-link text-gray-700 hover:text-blue-600 font-medium py-3 px-4 transition-all duration-300 relative overflow-hidden rounded-lg whitespace-nowrap block text-center"
          >
            <span className="relative z-10">{item.label}</span>
            <div className="nav-link-bg absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 transform scale-x-0 origin-left transition-transform duration-300"></div>
          </Link>
        ))}

        {/* 产品分类下拉菜单 */}
        <div
          className="relative group nav-item"
          onMouseEnter={() => handleMouseEnter('products')}
          onMouseLeave={handleMouseLeave}
        >
          <div className="relative">
            <Link
              href="/products"
              className="nav-link text-gray-700 hover:text-blue-600 font-medium py-3 px-4 transition-all duration-300 relative overflow-hidden rounded-lg whitespace-nowrap block text-center flex items-center"
              onClick={(e) => {
                // 在移动端，点击时切换下拉菜单而不是直接跳转
                if (window.innerWidth < 1024) {
                  handleMobileToggle('products', e);
                }
              }}
              onKeyDown={(e) => handleKeyDown(e, 'products')}
              tabIndex={0}
              role="button"
              aria-expanded={activeDropdown === 'products'}
              aria-haspopup="true"
            >
              <span className="relative z-10 flex items-center">
                设备推荐
                <svg
                  className={`ml-1 h-4 w-4 transition-transform duration-200 ${
                    activeDropdown === 'products' ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </span>
              <div className="nav-link-bg absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 transform scale-x-0 origin-left transition-transform duration-300"></div>
            </Link>
          </div>

          {/* 下拉菜单 */}
          {activeDropdown === 'products' && productCategories.length > 0 && (
            <div
              ref={dropdownRef}
              className={`dropdown-menu absolute top-full left-1/2 transform -translate-x-1/2 w-64 bg-white border border-gray-200 shadow-lg rounded-lg z-[9999] overflow-hidden transition-all duration-200 ease-out ${
                activeDropdown === 'products'
                  ? 'opacity-100 visible translate-y-0'
                  : 'opacity-0 invisible -translate-y-2'
              }`}
              style={{
                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                marginTop: '1px' // 减少间隙，确保鼠标移动连续性
              }}
              onMouseEnter={handleDropdownMouseEnter}
              onMouseLeave={handleDropdownMouseLeave}
            >
              {/* 连接区域 - 确保鼠标移动连续性 */}
              <div className="absolute -top-2 left-0 right-0 h-2 bg-transparent"></div>

              {/* Simple Arrow */}
              <div className="absolute -top-1 left-1/2 transform -translate-x-1/2">
                <div className="w-2 h-2 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
              </div>

              <div className="py-2">
                {/* 查看全部产品 */}
                <Link
                  href="/products"
                  className="dropdown-item block px-4 py-3 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-150 relative group"
                >
                  <span className="flex items-center justify-between">
                    <span className="flex items-center">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-150"></span>
                      <span className="font-medium">查看全部产品</span>
                    </span>
                    <svg className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-150 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </span>
                </Link>

                <div className="border-t border-gray-100 my-1"></div>

                {/* 产品分类 */}
                {productCategories.map((category, index) => (
                  <Link
                    key={category.id}
                    href={`/products/category/${category.slug}`}
                    className={`dropdown-item block px-4 py-3 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-150 relative group ${
                      activeDropdown === 'products' ? 'animate-fadeIn' : ''
                    }`}
                    style={{
                      animationDelay: `${index * 0.05}s`,
                      animationFillMode: 'both'
                    }}
                  >
                    <span className="flex items-center justify-between">
                      <span className="flex items-center">
                        <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-150"></span>
                        <span className="font-medium">{category.name}</span>
                      </span>
                      <svg className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-150 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                    {category.description && (
                      <div className="text-xs text-gray-500 mt-1 ml-6">{category.description}</div>
                    )}
                    {category.product_count !== undefined && (
                      <div className="text-xs text-blue-500 mt-1 ml-6">{category.product_count} 个产品</div>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 其他静态导航项 */}
        {otherNavItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className="nav-link text-gray-700 hover:text-blue-600 font-medium py-3 px-4 transition-all duration-300 relative overflow-hidden rounded-lg whitespace-nowrap block text-center"
          >
            <span className="relative z-10">{item.label}</span>
            <div className="nav-link-bg absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 transform scale-x-0 origin-left transition-transform duration-300"></div>
          </Link>
        ))}
      </div>
    </nav>
  );
}
