'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import DynamicNavigation from './DynamicNavigation';

const Header = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <header className={`bg-white shadow-md transition-all duration-500 fixed top-0 left-0 right-0 z-50 ${isVisible ? 'header-visible' : 'header-hidden'}`}>
      {/* Logo and Navigation */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center flex-shrink-0">
            <div className="logo-container w-44 h-10 bg-gradient-to-r from-blue-600 to-blue-700 flex items-center justify-center text-white font-bold text-base rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
              等离子清洗专家
            </div>
          </Link>

          {/* Navigation */}
          <DynamicNavigation className="hidden lg:flex items-center justify-center flex-1" />

          {/* Mobile Menu Button */}
          <button className="lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors duration-300 flex-shrink-0">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
