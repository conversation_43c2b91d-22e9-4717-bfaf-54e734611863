import React, { useEffect, useState } from 'react';
import { Typography, Progress, Space, Tag } from 'antd';
import { usePlasmaStore, selectCurrentMaterial, selectCurrentProcess, selectIsProcessRunning, selectAnimationState } from '@/stores/plasmaStore';
import { MATERIAL_SPECIFIC_PARAMETERS } from '@/constants/plasma';
import { MaterialType, ProcessType, AnimationState } from '@/types/plasma';

const { Text } = Typography;

interface MaterialEffectsProps {
  className?: string;
}

const MaterialEffects: React.FC<MaterialEffectsProps> = ({ className }) => {
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const currentProcess = usePlasmaStore(selectCurrentProcess);
  const isProcessRunning = usePlasmaStore(selectIsProcessRunning);
  const animationState = usePlasmaStore(selectAnimationState);
  const { scene } = usePlasmaStore();
  const [effectProgress, setEffectProgress] = useState(0);

  // 获取材料特定参数
  const getMaterialParams = () => {
    if (!currentMaterial || !currentProcess) return null;
    return (MATERIAL_SPECIFIC_PARAMETERS as Record<string, Record<string, Record<string, unknown>>>)[currentMaterial.id]?.[currentProcess.id];
  };

  const materialParams = getMaterialParams();

  // 动画效果进度 - 基于实际处理进度
  useEffect(() => {
    if (isProcessRunning && materialParams) {
      // 使用实际的处理进度
      setEffectProgress(scene.processProgress);
    } else if (animationState === AnimationState.COMPLETED) {
      setEffectProgress(100);
    } else {
      setEffectProgress(0);
    }
  }, [isProcessRunning, materialParams, scene.processProgress, animationState]);

  // 获取材料特定的效果描述
  const getMaterialEffects = () => {
    if (!currentMaterial || !currentProcess) return [];

    const effects = [];

    switch (currentMaterial.id) {
      case MaterialType.POLYMER:
        switch (currentProcess.id) {
          case ProcessType.ACTIVATION:
            effects.push(
              { name: '表面活化', description: '增加表面极性基团', color: '#4CAF50', progress: effectProgress },
              { name: '亲水性改善', description: '降低接触角', color: '#2196F3', progress: effectProgress * 0.8 },
              { name: '粘接性提升', description: '提高表面能', color: '#FF9800', progress: effectProgress * 0.9 }
            );
            break;
          case ProcessType.ETCHING:
            effects.push(
              { name: '表面清洁', description: '去除有机污染物', color: '#FF5722', progress: effectProgress },
              { name: '微观粗化', description: '增加表面积', color: '#9C27B0', progress: effectProgress * 0.7 },
              { name: '化学改性', description: '引入功能基团', color: '#00BCD4', progress: effectProgress * 0.6 }
            );
            break;
          case ProcessType.COATING:
            effects.push(
              { name: '薄膜沉积', description: '形成保护层', color: '#00BCD4', progress: effectProgress },
              { name: '功能化', description: '赋予特殊性能', color: '#4CAF50', progress: effectProgress * 0.8 },
              { name: '耐久性提升', description: '增强稳定性', color: '#FF9800', progress: effectProgress * 0.9 }
            );
            break;
        }
        break;

      case MaterialType.METAL:
        switch (currentProcess.id) {
          case ProcessType.ACTIVATION:
            effects.push(
              { name: '氧化层去除', description: '还原表面氧化物', color: '#9E9E9E', progress: effectProgress },
              { name: '表面清洁', description: '去除油污和杂质', color: '#607D8B', progress: effectProgress * 0.9 },
              { name: '活性提升', description: '增加反应活性', color: '#FF6D00', progress: effectProgress * 0.8 }
            );
            break;
          case ProcessType.ETCHING:
            effects.push(
              { name: '深度刻蚀', description: '精确去除材料', color: '#FF5722', progress: effectProgress },
              { name: '表面粗化', description: '增加锚固点', color: '#9C27B0', progress: effectProgress * 0.8 },
              { name: '导电性改善', description: '优化电学性能', color: '#2196F3', progress: effectProgress * 0.7 }
            );
            break;
          case ProcessType.COATING:
            effects.push(
              { name: '金属镀层', description: '沉积功能涂层', color: '#607D8B', progress: effectProgress },
              { name: '耐腐蚀性', description: '提高防护能力', color: '#4CAF50', progress: effectProgress * 0.9 },
              { name: '硬度增强', description: '改善机械性能', color: '#FF9800', progress: effectProgress * 0.8 }
            );
            break;
        }
        break;

      case MaterialType.GLASS_CERAMIC:
        switch (currentProcess.id) {
          case ProcessType.ACTIVATION:
            effects.push(
              { name: '表面清洁', description: '去除表面污染', color: '#2196F3', progress: effectProgress },
              { name: '键合改善', description: '增强分子间作用', color: '#00BCD4', progress: effectProgress * 0.8 },
              { name: '光学优化', description: '提高透明度', color: '#E1F5FE', progress: effectProgress * 0.9 }
            );
            break;
          case ProcessType.ETCHING:
            effects.push(
              { name: '精密刻蚀', description: '高精度图案化', color: '#FF9800', progress: effectProgress },
              { name: '表面纹理', description: '创建微结构', color: '#9C27B0', progress: effectProgress * 0.7 },
              { name: '应力释放', description: '减少内应力', color: '#4CAF50', progress: effectProgress * 0.8 }
            );
            break;
          case ProcessType.COATING:
            effects.push(
              { name: '透明涂层', description: '保持光学性能', color: '#00E676', progress: effectProgress },
              { name: '防污处理', description: '易清洁表面', color: '#2196F3', progress: effectProgress * 0.9 },
              { name: '硬度提升', description: '增强耐磨性', color: '#FF9800', progress: effectProgress * 0.8 }
            );
            break;
        }
        break;
    }

    return effects;
  };

  const effects = getMaterialEffects();

  if (!currentMaterial || !currentProcess || !materialParams) {
    return (
      <div className={className} style={{ padding: '16px', textAlign: 'center' }}>
        <Text style={{ color: '#b0b0b0' }}>请选择材料和工艺以查看效果</Text>
      </div>
    );
  }

  return (
    <div className={`material-effects ${className}`}>
      <div className="effects-header">
        <Text strong style={{
          fontSize: '14px',
          color: '#ffffff',
          textShadow: '0 2px 4px rgba(0,0,0,0.8)'
        }}>
          {currentMaterial.displayName} - {currentProcess.displayName}
        </Text>
        <Tag
          color={
            animationState === AnimationState.RUNNING ? 'processing' :
            animationState === AnimationState.PAUSED ? 'warning' :
            animationState === AnimationState.COMPLETED ? 'success' : 'default'
          }
          style={{ marginLeft: '8px' }}
        >
          {animationState === AnimationState.RUNNING ? '处理中' :
           animationState === AnimationState.PAUSED ? '已暂停' :
           animationState === AnimationState.COMPLETED ? '已完成' : '待机'}
        </Tag>
      </div>

      <div className="effects-content">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {effects.map((effect, index) => (
            <div key={index} className="effect-item">
              <div className="effect-header">
                <Text strong style={{
                  fontSize: '12px',
                  color: '#ffffff',
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)'
                }}>
                  {effect.name}
                </Text>
                <Text style={{
                  fontSize: '10px',
                  color: '#d0d0d0',
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)'
                }}>
                  {effect.description}
                </Text>
              </div>
              <Progress
                percent={animationState !== AnimationState.IDLE ? effect.progress : 0}
                strokeColor={effect.color}
                size="small"
                showInfo={false}
                style={{ marginTop: '4px' }}
                status={
                  animationState === AnimationState.COMPLETED ? 'success' :
                  animationState === AnimationState.PAUSED ? 'normal' : 'active'
                }
              />
              {animationState !== AnimationState.IDLE && (
                <Text style={{
                  fontSize: '10px',
                  color: effect.color,
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)'
                }}>
                  {effect.progress.toFixed(0)}%
                </Text>
              )}
            </div>
          ))}
        </Space>
      </div>

      <style jsx>{`
        .material-effects {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 12px;
        }

        .effects-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .effect-item {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          padding: 8px;
          transition: all 0.3s ease;
        }

        .effect-item:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }

        .effect-header {
          display: flex;
          flex-direction: column;
          gap: 2px;
        }

        @media (max-width: 768px) {
          .effects-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }
      `}</style>
    </div>
  );
};

export default MaterialEffects;
